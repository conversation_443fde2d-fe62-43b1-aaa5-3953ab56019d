/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "bt_node_opm_driver.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include "thorlabs_optical_power_meter.h"
#include <chrono>
#include <future>
namespace robosense::lidar
{

// ============================================================================
// OPMConnect 节点实现
// ============================================================================

OPMConnect::OPMConnect(const std::string& _name, const BT::NodeConfig& _config) : BT::CoroActionNode(_name, _config) {}

BT::PortsList OPMConnect::providedPorts()
{
  return { BT::InputPort<std::string>("opm_sn", "*********", "光功率计编号"),
           BT::InputPort<double>("power_range", 0.02, "测量的功率范围(W)"),
           BT::InputPort<double>("wave_length", 905.0, "测量波长值(nm)"),
           BT::OutputPort<std::shared_ptr<ThorlabsOpticalPowerMeter>>("opm_handle", "光功率计设备句柄") };
}

BT::NodeStatus OPMConnect::tick()
{
  // 获取输入参数
  auto opm_sn      = getInput<std::string>("opm_sn");
  auto power_range = getInput<double>("power_range");
  auto wave_length = getInput<double>("wave_length");

  if (!opm_sn || !power_range || !wave_length)
  {
    LOG_ERROR("OPMConnect: 无法获取输入参数");
    return BT::NodeStatus::FAILURE;
  }

  LOG_INFO("OPMConnect: 开始连接设备 {}", opm_sn.value());

  // 创建设备句柄
  if (!opm_handle_)
  {
    opm_handle_ = std::make_shared<ThorlabsOpticalPowerMeter>(ThorlabsOpticalPowerMeter::PowerMeterType::PM100X);
  }

  // 启动异步连接
  if (!connection_future_.valid())
  {
    connection_future_ = std::async(std::launch::async, [this, opm_sn, power_range, wave_length]() {
      return opm_handle_->open(opm_sn.value(), power_range.value(), wave_length.value());
    });
  }

  // 检查连接状态
  if (connection_future_.wait_for(std::chrono::milliseconds(0)) == std::future_status::ready)
  {
    bool success = connection_future_.get();
    if (!success)
    {
      LOG_ERROR("OPMConnect: 连接失败, error : {}", opm_handle_->getErrorMsg());
      return BT::NodeStatus::FAILURE;
    }

    setOutput("opm_handle", opm_handle_);

    LOG_INFO("OPMConnect: 连接成功");
    return BT::NodeStatus::SUCCESS;
  }

  // 连接仍在进行中
  return BT::NodeStatus::RUNNING;
}

void OPMConnect::halt() { LOG_INFO("OPMConnect: 节点被中断"); }

// ============================================================================
// OPMDisconnect 节点实现
// ============================================================================

OPMDisconnect::OPMDisconnect(const std::string& _name, const BT::NodeConfig& _config) :
  BT::StatefulActionNode(_name, _config)
{}

BT::PortsList OPMDisconnect::providedPorts()
{
  return { BT::InputPort<std::shared_ptr<ThorlabsOpticalPowerMeter>>("opm_handle", "光功率计设备句柄") };
}

BT::NodeStatus OPMDisconnect::onStart()
{
  // 获取设备句柄
  auto handle_result = getInput<std::shared_ptr<ThorlabsOpticalPowerMeter>>("opm_handle");
  if (!handle_result)
  {
    LOG_ERROR("OPMDisconnect: 无法获取设备句柄");
    return BT::NodeStatus::FAILURE;
  }

  opm_handle_ = handle_result.value();

  LOG_INFO("OPMDisconnect: 开始断开连接");

  // 启动异步断开
  disconnect_future_ = std::async(std::launch::async, [handle = opm_handle_]() { return handle->close(); });

  return BT::NodeStatus::RUNNING;
}

BT::NodeStatus OPMDisconnect::onRunning()
{
  // 检查断开状态
  if (disconnect_future_.wait_for(std::chrono::milliseconds(0)) == std::future_status::ready)
  {
    bool success = disconnect_future_.get();
    if (!success)
    {
      LOG_ERROR("OPMDisconnect: 断开连接失败");
      return BT::NodeStatus::FAILURE;
    }

    LOG_INFO("OPMDisconnect: 断开连接成功");
    return BT::NodeStatus::SUCCESS;
  }

  return BT::NodeStatus::RUNNING;
}

void OPMDisconnect::onHalted() { LOG_INFO("OPMDisconnect: 节点被中断"); }

// ============================================================================
// OPMReadPower 节点实现
// ============================================================================

OPMReadPower::OPMReadPower(const std::string& _name, const BT::NodeConfig& _config) : BT::CoroActionNode(_name, _config)
{}

BT::PortsList OPMReadPower::providedPorts()
{
  return { BT::InputPort<std::shared_ptr<ThorlabsOpticalPowerMeter>>("opm_handle", "光功率计设备句柄"),
           BT::OutputPort<double>("power_mw", "读取的光功率值(mW)") };
}

BT::NodeStatus OPMReadPower::tick()
{
  // 获取设备句柄
  auto handle_ptr = getInput<std::shared_ptr<ThorlabsOpticalPowerMeter>>("opm_handle");
  if (!handle_ptr)
  {
    LOG_ERROR("OPMReadPower: 无法获取设备句柄");
    return BT::NodeStatus::FAILURE;
  }

  opm_handle_ = handle_ptr.value();

  LOG_DEBUG("OPMReadPower: 开始读取光功率");

  // 启动异步读取
  if (!read_future_.valid())
  {
    read_future_ = std::async(std::launch::async, [this]() -> std::pair<bool, double> {
      double power_w = 0.0;
      bool success   = opm_handle_->measurePower(power_w);
      return std::make_pair(success, power_w * 1000);
    });
  }

  // 检查读取状态
  if (read_future_.wait_for(std::chrono::milliseconds(0)) == std::future_status::ready)
  {
    auto result = read_future_.get();
    if (!result.first)
    {
      LOG_ERROR("OPMReadPower: 读取失败, error: {}", opm_handle_->getErrorMsg());
      return BT::NodeStatus::FAILURE;
    }
    setOutput("power_mw", result.second);
    LOG_INFO("OPMReadPower: 读取成功，功率值: {:.3f} mW", result.second);
    return BT::NodeStatus::SUCCESS;
  }

  // 读取仍在进行中
  return BT::NodeStatus::RUNNING;
}

void OPMReadPower::halt() { LOG_INFO("OPMReadPower: 节点被中断"); }

// ============================================================================
// OPMConfigure 节点实现
// ============================================================================

OPMConfigure::OPMConfigure(const std::string& _name, const BT::NodeConfig& _config) :
  BT::StatefulActionNode(_name, _config)
{}

BT::PortsList OPMConfigure::providedPorts()
{
  return {
    BT::InputPort<std::shared_ptr<ThorlabsOpticalPowerMeter>>("opm_handle", "光功率计设备句柄"),
    BT::InputPort<double>("wave_length_nm", 905.0, "测量波长(nm)"),
    BT::InputPort<int>("average_count", 100, "测量平均次数"),
    BT::InputPort<double>("power_range", 0.02, "测量的功率范围(W)"),
  };
}

BT::NodeStatus OPMConfigure::onStart()
{
  // 获取设备句柄
  auto handle_ptr = getInput<std::shared_ptr<ThorlabsOpticalPowerMeter>>("opm_handle");
  if (!handle_ptr)
  {
    LOG_ERROR("OPMConfigure: 无法获取设备句柄");
    return BT::NodeStatus::FAILURE;
  }

  opm_handle_ = handle_ptr.value();

  // 获取配置参数
  auto average_count = getInput<int>("average_count");
  auto power_range   = getInput<int>("power_range");

  if (!average_count || !power_range)
  {
    LOG_ERROR("OPMConfigure: 无法获取配置参数");
    return BT::NodeStatus::FAILURE;
  }

  LOG_INFO("OPMConfigure: 开始配置设备，平均次数: {}, 测量功率最大值: {}", average_count.value(), power_range.value());

  // 设置平均次数
  if (!opm_handle_->setAvgCnt(average_count.value()))
  {
    LOG_ERROR("OPMConfigure: 设置平均次数失败, error: {}", opm_handle_->getErrorMsg());
    return BT::NodeStatus::FAILURE;
  }

  // 设置测量范围
  if (!opm_handle_->setPowerRange(power_range.value()))
  {
    LOG_ERROR("OPMConfigure: 设置测量范围失败, error: {}", opm_handle_->getErrorMsg());
    return BT::NodeStatus::FAILURE;
  }

  LOG_INFO("OPMConfigure: 配置完成");
  return BT::NodeStatus::SUCCESS;
}

BT::NodeStatus OPMConfigure::onRunning() { return BT::NodeStatus::SUCCESS; }

void OPMConfigure::onHalted() { LOG_INFO("OPMConfigure: 节点被中断"); }

}  // namespace robosense::lidar
