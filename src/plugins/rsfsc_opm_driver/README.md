# Thorlabs 光功率计驱动插件

## 概述

本插件为工装平台测试软件提供 Thorlabs 光功率计的驱动支持，允许用户在 BehaviorTree 流程中集成光功率测量功能。支持 PM100X 和 PM101X 系列光功率计。

## 功能特性

- **设备连接管理**：支持通过设备序列号连接 Thorlabs 光功率计
- **功率测量**：实时读取光功率值（mW 单位）
- **设备配置**：支持设置测量波长、功率范围等参数
- **异步操作**：所有操作均为异步执行，不阻塞 UI 线程
- **完整的错误处理**：提供详细的错误信息和日志记录

## 支持的节点类型

### 1. OPMConnect - 连接光功率计

**功能**：建立与光功率计的连接并进行初始化配置
**输入参数**：

- `opm_sn`：光功率计序列号（默认：*********）
- `power_range`：测量的功率范围，单位 W（默认：0.02，范围：0.0-100.0）
- `wave_length`：测量波长值，单位 nm（默认：905.0，范围：400.0-1700.0）

**输出参数**：

- `opm_handle`：设备句柄，用于后续操作

### 2. OPMDisconnect - 断开光功率计

**功能**：断开与光功率计的连接
**输入参数**：

- `opm_handle`：设备句柄

### 3. OPMReadPower - 读取光功率

**功能**：读取当前光功率值
**输入参数**：

- `opm_handle`：设备句柄

**输出参数**：

- `power_mw`：光功率值，单位 mW

### 4. OPMConfigure - 配置光功率计

**功能**：配置设备参数
**输入参数**：

- `opm_handle`：设备句柄
- `wave_length`：测量波长值，单位 nm（默认：905.0，范围：400.0-1700.0）
- `power_range`：测量的功率范围，单位 W（默认：0.02，范围：0.0-100.0）

## 使用示例

### 基本测量流程

```xml
<Sequence>
  <!-- 连接设备并初始化配置 -->
  <OPMConnect
      opm_sn="*********"
      power_range="0.02"
      wave_length="905.0"
      opm_handle="{opm_handle}" />

  <!-- 读取功率 -->
  <OPMReadPower opm_handle="{opm_handle}" power_mw="{power_value}" />

  <!-- 添加测量数据 -->
  <AddNumericMeasure
      label="光功率"
      lower_limit="0.0"
      upper_limit="20.0"
      unit="mW"
      variable_name="power_value"
      data_type="0" />

  <!-- 断开连接 -->
  <OPMDisconnect opm_handle="{opm_handle}" />
</Sequence>
```

### 多次测量流程

```xml
<Sequence>
  <!-- 连接设备 -->
  <OPMConnect
      opm_sn="*********"
      power_range="0.02"
      wave_length="905.0"
      opm_handle="{opm_handle}" />

  <!-- 重复测量3次 -->
  <Repeat num_cycles="3">
    <Sequence>
      <OPMReadPower opm_handle="{opm_handle}" power_mw="{power_value}" />
      <Sleep 延时="1000" />
    </Sequence>
  </Repeat>

  <OPMDisconnect opm_handle="{opm_handle}" />
</Sequence>
```

### 动态配置测量流程

```xml
<Sequence>
  <!-- 连接设备 -->
  <OPMConnect
      opm_sn="*********"
      power_range="0.02"
      wave_length="905.0"
      opm_handle="{opm_handle}" />

  <!-- 重新配置设备参数 -->
  <OPMConfigure
      opm_handle="{opm_handle}"
      wave_length="1550.0"
      power_range="0.05" />

  <!-- 读取功率 -->
  <OPMReadPower opm_handle="{opm_handle}" power_mw="{power_value}" />

  <OPMDisconnect opm_handle="{opm_handle}" />
</Sequence>
```

## 技术实现

### 架构设计

- **ThorlabsOpticalPowerMeter**：设备驱动类，管理设备连接和操作
- **异步节点**：使用 BT::CoroActionNode 和 BT::StatefulActionNode 实现异步操作
- **错误处理**：完善的错误检查和日志记录
- **设备类型支持**：支持 PM100X 和 PM101X 系列光功率计

### 当前实现状态

- ✅ 插件框架完成
- ✅ 节点注册和 UI 集成
- ✅ 实际硬件驱动集成（基于 lib/optical_power_meter_driver）
- ✅ 异步操作支持
- ✅ 完整的错误处理机制

### 核心类说明

#### ThorlabsOpticalPowerMeter 类

该类位于 `lib/optical_power_meter_driver` 中，提供以下主要功能：

- **设备发现**：`findRsrc()` 方法查找可用设备
- **设备连接**：`open()` 方法建立连接并初始化设备
- **功率测量**：`measurePower()` 方法读取光功率值
- **参数配置**：`setAvgCnt()` 和 `setPowerRange()` 方法配置设备参数
- **设备断开**：`close()` 方法安全断开连接

#### 节点实现特点

1. **OPMConnect**：使用 `BT::CoroActionNode` 实现异步连接
2. **OPMDisconnect**：使用 `BT::StatefulActionNode` 实现状态管理
3. **OPMReadPower**：使用 `BT::CoroActionNode` 实现异步读取
4. **OPMConfigure**：使用 `BT::StatefulActionNode` 实现配置管理

## 编译和安装

插件会自动随主项目编译，生成的库文件位于：

```text
build/plugins/librsfsc_opm_driver.so
```

## 故障排除

### 常见问题

1. **设备连接失败**
   - 检查设备序列号是否正确
   - 确认设备驱动已安装（VISA 驱动）
   - 验证设备是否被其他程序占用
   - 检查 USB 连接是否稳定

2. **读取数据异常**
   - 检查设备是否正确配置
   - 确认波长设置是否匹配传感器类型
   - 验证功率范围设置是否合适
   - 检查设备连接状态

3. **插件加载失败**
   - 确认插件文件存在
   - 检查依赖库是否完整
   - 查看应用程序日志
   - 验证 optical_power_meter_driver 库是否正确链接

### 调试信息

插件会输出详细的调试日志，可通过以下方式查看：

```bash
# 查看实时日志
tail -f logs/fixture_test.log | grep OPM

# 搜索错误信息
grep "OPM.*ERROR" logs/fixture_test.log
```

## 版本历史

- **v1.0.0**：完整版本，包含实际硬件驱动支持
  - 集成 ThorlabsOpticalPowerMeter 驱动类
  - 支持 PM100X 和 PM101X 系列设备
  - 实现异步操作和完整错误处理
  - 提供四个核心节点：连接、断开、读取、配置

## 许可证

版权所有 © 2025 RoboSense。保留所有权利。
