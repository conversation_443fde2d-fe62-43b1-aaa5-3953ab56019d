﻿## Changelog

## 2.1.0 2025-05-27

### Changed

- 适配最新的`RSFSCLog`库
- 添加`qt example`

## 2.0.0 2024-09-25

### Added

- 添加支持`thorlabs`的`PM100X`及`PM101X`型号功率计
- 支持`linux`及`windows`双系统

## 1.1.2 2023-10-09

### Changed

- 修改`open`函数,支持配置不同的波段, 默认为`905`nm

### Fixed

- 修复`measurePower`不需要使用`TLPM_findRsrc`, 避免多线程操作导致线程不安全
- 修复连接对应 ` SN` 的功率计时, 需判断是否 `SN` 为空情况

## 1.1.1 2023-03-20

### Fixed

- 修复由于修改 CI 检查导致的错误判断

## 1.1.0 2023-02-09

### Added

- 增加 CI 检查，并做代码优化修改

## 1.0.1 2022-10-20

### Fixed

- fix the error spelling words

## 1.0.0 2022-09-02

### Changed

- use mutex in open function to make sure thread safety

## 0.1.1 2022-07-29

### Fixed

- fix to use the error to find sn in string

## 0.1.0 2022-07-04

### Added

- First commit
