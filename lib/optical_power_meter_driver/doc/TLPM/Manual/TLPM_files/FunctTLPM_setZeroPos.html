<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_setZeroPos</title>
  </head>
  <body>
    <h1>TLPM_setZeroPos</h1>
    <p class="syntax">
      ViStatus TLPM_setZeroPos (ViSession instrumentHandle, ViReal64 zeroX,
                          ViReal64 zeroY, ViUInt16 channel);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    Use this command to set beam position zero correction coordinate in um. Zero parameter is not stored persistently. It will be lost after reboot!
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td class="paramName">zeroX</td>
        <td class="paramDataType">ViReal64</td>
        <td>
    This parameter set the zero x in µm.
        </td>
      </tr>
      <tr>
        <td class="paramName">zeroY</td>
        <td class="paramDataType">ViReal64</td>
        <td>
    This parameter set the zero y in µm.
        </td>
      </tr>
      <tr>
        <td class="paramName">channel</td>
        <td class="paramDataType">ViUInt16</td>
        <td>
    Number of the sensor channel.
    <br/>
    Default: 1 for non multi channel devices
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassCorrection.html">Correction</a><br/>
      <a href="ClassConfigure.html">Configure</a><br/>
      <a href="ClassMeasure.html">Measure</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>