<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_setDigIoPinMode</title>
  </head>
  <body>
    <h1>TLPM_setDigIoPinMode</h1>
    <p class="syntax">
      ViStatus TLPM_setDigIoPinMode (ViSession instrumentHandle, ViInt16 pinNumber,
                               ViUInt16 pinMode);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function sets the digital I/O port direction.
    <br/>
    Note: The function is only available on PM200, PM400 and PM103
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td class="paramName">pinNumber</td>
        <td class="paramDataType">ViInt16</td>
        <td>
    Number of the Pin.
    <br/>
    Range: 1-7
        </td>
      </tr>
      <tr>
        <td class="paramName">pinMode</td>
        <td class="paramDataType">ViUInt16</td>
        <td>
    This parameter specifies the I/O port direction.
    <br/>
    Input:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DIGITAL_IO_CONFIG_INPUT&nbsp;&nbsp;&nbsp;(0)
    <br/>
    Output:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DIGITAL_IO_CONFIG_OUTPUT&nbsp;&nbsp;(1)
    <br/>
    Alternative: DIGITAL_IO_CONFIG_ALT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(2)
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassDigital%20I,O%20PM103.html">Digital I/O PM103</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>