<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_startMeasurementSequence</title>
  </head>
  <body>
    <h1>TLPM_startMeasurementSequence</h1>
    <p class="syntax">
      ViStatus TLPM_startMeasurementSequence (ViSession instrumentHandle,
                                        ViUInt32 autoTriggerDelay,
                                        ViPBoolean triggerForced);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function send the SCPI Command &quot;INIT&quot; to the device.
    <br/>
    PM103:
    <br/>
    Then it calls TLPM_readRegister for the register TLPM_REG_OPER_COND if there is new data to read
    <br/>
    If this method is successfull you can call getMeasurementSequence or getMeasurementSequenceHWTrigger
    <br/>
    PM101 special:
    <br/>
    Just the INIT command is send to the device.
    <br/>
    Note: The function is only available on PM103 and PM101 special.
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td class="paramName">autoTriggerDelay</td>
        <td class="paramDataType">ViUInt32</td>
        <td>
    PM103:
    <br/>
    The unit of this parameter is milliseconds.
    <br/>
    If this parameter bigger then zero, the method will
    <br/>
    wait the time in milliseconds to send the SCPI command:&quot;TRIGer:ARRay:FORce&quot;.
    <br/>
    This command will force the measurement.
    <br/>
    PM101 special:
    <br/>
    Not used.
        </td>
      </tr>
      <tr>
        <td valign="top"><em class="label">Output</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">triggerForced</td>
        <td class="paramDataType">ViBoolean (passed by reference)</td>
        <td>
    PM103:
    <br/>
    Return parameter is TRUE if the command:&quot;TRIGer:ARRay:FORce&quot;. was internally send to the device. See parameter &quot;AutoTriggerDelay&quot;.
    <br/>
    PM101 special:
    <br/>
    Not used.
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassArray%20Measurement.html">Array Measurement</a><br/>
      <a href="ClassMeasure.html">Measure</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>