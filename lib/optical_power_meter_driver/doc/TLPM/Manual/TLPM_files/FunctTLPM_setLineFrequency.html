<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_setLineFrequency</title>
  </head>
  <body>
    <h1>TLPM_setLineFrequency</h1>
    <p class="syntax">
      ViStatus TLPM_setLineFrequency (ViSession instrumentHandle,
                                ViInt16 lineFrequency);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function selects the line frequency.
    <br/>
    Notes:
    <br/>
    (1) The function is only available on PM100A, PM100D, PM100USB, PM200.
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td class="paramName">lineFrequency</td>
        <td class="paramDataType">ViInt16</td>
        <td>
    This parameter specifies the line frequency.
    <br/>
    Accepted values:
    <br/>
    TLPM_LINE_FREQ_50 (50): 50Hz
    <br/>
    TLPM_LINE_FREQ_60 (60): 60Hz
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This value shows the status code returned by the function call.
    <br/>
    For Status Codes see function &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassLine%20Frequency.html">Line Frequency</a><br/>
      <a href="ClassSystem.html">System</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>