<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_writeRegister</title>
  </head>
  <body>
    <h1>TLPM_writeRegister</h1>
    <p class="syntax">
      ViStatus TLPM_writeRegister (ViSession instrumentHandle, ViInt16 registerID,
                             ViInt16 value);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function writes the content of any writable instrument register. Refer to your instrument's user's manual for more details on status structure registers.
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td class="paramName">registerID</td>
        <td class="paramDataType">ViInt16</td>
        <td>
    Specifies the register to be used for operation. This parameter can be any of the following constants:
    <br/>
    TLPM_REG_SRE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(1): Service Request Enable
    <br/>
    TLPM_REG_ESE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(3): Standard Event Enable
    <br/>
    TLPM_REG_OPER_ENAB&nbsp;&nbsp;&nbsp;(6): Operation Event Enable Register
    <br/>
    TLPM_REG_OPER_PTR&nbsp;&nbsp;&nbsp;&nbsp;(7): Operation Positive Transition
    <br/>
    TLPM_REG_OPER_NTR&nbsp;&nbsp;&nbsp;&nbsp;(8): Operation Negative Transition
    <br/>
    TLPM_REG_QUES_ENAB&nbsp;&nbsp;(11): Questionable Event Enable Reg.
    <br/>
    TLPM_REG_QUES_PTR&nbsp;&nbsp;&nbsp;(12): Questionable Positive Transition
    <br/>
    TLPM_REG_QUES_NTR&nbsp;&nbsp;&nbsp;(13): Questionable Negative Transition
    <br/>
    TLPM_REG_MEAS_ENAB&nbsp;&nbsp;(16): Measurement Event Enable Register
    <br/>
    TLPM_REG_MEAS_PTR&nbsp;&nbsp;&nbsp;(17): Measurement Positive Transition
    <br/>
    TLPM_REG_MEAS_NTR&nbsp;&nbsp;&nbsp;(18): Measurement Negative Transition
    <br/>
    TLPM_REG_AUX_ENAB&nbsp;&nbsp;&nbsp;(21): Auxiliary Event Enable Register
    <br/>
    TLPM_REG_AUX_PTR&nbsp;&nbsp;&nbsp;&nbsp;(22): Auxiliary Positive Transition
    <br/>
    TLPM_REG_AUX_NTR&nbsp;&nbsp;&nbsp;&nbsp;(23): Auxiliary Negative Transition
        </td>
      </tr>
      <tr>
        <td class="paramName">value</td>
        <td class="paramDataType">ViInt16</td>
        <td>
    This parameter specifies the new value of the selected register.
    <br/>
    These register bits are defined:
    <br/>
    STATUS BYTE bits (see IEEE488.2-1992 §11.2)
    <br/>
    TLPM_STATBIT_STB_AUX&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x01): Auxiliary summary
    <br/>
    TLPM_STATBIT_STB_MEAS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x02): Device Measurement Summary
    <br/>
    TLPM_STATBIT_STB_EAV&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x04): Error available
    <br/>
    TLPM_STATBIT_STB_QUES&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x08): Questionable Status Summary
    <br/>
    TLPM_STATBIT_STB_MAV&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x10): Message available
    <br/>
    TLPM_STATBIT_STB_ESB&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x20): Event Status Bit
    <br/>
    TLPM_STATBIT_STB_MSS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x40): Master summary status
    <br/>
    TLPM_STATBIT_STB_OPER&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x80): Operation Status Summary
    <br/>
    STANDARD EVENT STATUS REGISTER bits (see IEEE488.2-1992 §11.5.1)
    <br/>
    TLPM_STATBIT_ESR_OPC&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x01): Operation complete
    <br/>
    TLPM_STATBIT_ESR_RQC&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x02): Request control
    <br/>
    TLPM_STATBIT_ESR_QYE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x04): Query error
    <br/>
    TLPM_STATBIT_ESR_DDE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x08): Device-Specific error
    <br/>
    TLPM_STATBIT_ESR_EXE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x10): Execution error
    <br/>
    TLPM_STATBIT_ESR_CME&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x20): Command error
    <br/>
    TLPM_STATBIT_ESR_URQ&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x40): User request
    <br/>
    TLPM_STATBIT_ESR_PON&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x80): Power on
    <br/>
    QUESTIONABLE STATUS REGISTER bits (see SCPI 99.0 §9)
    <br/>
    TLPM_STATBIT_QUES_VOLT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0001): Questionable voltage measurement
    <br/>
    TLPM_STATBIT_QUES_CURR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0002): Questionable current measurement
    <br/>
    TLPM_STATBIT_QUES_TIME&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0004): Questionable time measurement
    <br/>
    TLPM_STATBIT_QUES_POW&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0008): Questionable power measurement
    <br/>
    TLPM_STATBIT_QUES_TEMP&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0010): Questionable temperature measurement
    <br/>
    TLPM_STATBIT_QUES_FREQ&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0020): Questionable frequency measurement
    <br/>
    TLPM_STATBIT_QUES_PHAS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0040): Questionable phase measurement
    <br/>
    TLPM_STATBIT_QUES_MOD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0080): Questionable modulation measurement
    <br/>
    TLPM_STATBIT_QUES_CAL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0100): Questionable calibration
    <br/>
    TLPM_STATBIT_QUES_ENER&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0200): Questionable energy measurement
    <br/>
    TLPM_STATBIT_QUES_10&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0400): Reserved
    <br/>
    TLPM_STATBIT_QUES_11&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0800): Reserved
    <br/>
    TLPM_STATBIT_QUES_12&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x1000): Reserved
    <br/>
    TLPM_STATBIT_QUES_INST&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x2000): Instrument summary
    <br/>
    TLPM_STATBIT_QUES_WARN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x4000): Command warning
    <br/>
    TLPM_STATBIT_QUES_15&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x8000): Reserved
    <br/>
    OPERATION STATUS REGISTER bits (see SCPI 99.0 §9)
    <br/>
    TLPM_STATBIT_OPER_CAL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0001): The instrument is currently performing a calibration.
    <br/>
    TLPM_STATBIT_OPER_SETT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0002): The instrument is waiting for signals to stabilize for measurements.
    <br/>
    TLPM_STATBIT_OPER_RANG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0004): The instrument is currently changing its range.
    <br/>
    TLPM_STATBIT_OPER_SWE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0008): A sweep is in progress.
    <br/>
    TLPM_STATBIT_OPER_MEAS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0010): The instrument is actively measuring.
    <br/>
    TLPM_STATBIT_OPER_TRIG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0020): The instrument is in a “wait for trigger” state of the trigger model.
    <br/>
    TLPM_STATBIT_OPER_ARM&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0040): The instrument is in a “wait for arm” state of the trigger model.
    <br/>
    TLPM_STATBIT_OPER_CORR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0080): The instrument is currently performing a correction (Auto-PID tune).
    <br/>
    TLPM_STATBIT_OPER_SENS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0100): Optical powermeter sensor connected and operable.
    <br/>
    TLPM_STATBIT_OPER_DATA&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0200): Measurement data ready for fetch.
    <br/>
    TLPM_STATBIT_OPER_THAC&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0400): Thermopile accelerator active.
    <br/>
    TLPM_STATBIT_OPER_11&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0800): Reserved
    <br/>
    TLPM_STATBIT_OPER_12&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x1000): Reserved
    <br/>
    TLPM_STATBIT_OPER_INST&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x2000): One of n multiple logical instruments is reporting OPERational status.
    <br/>
    TLPM_STATBIT_OPER_PROG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x4000): A user-defined programming is currently in the run state.
    <br/>
    TLPM_STATBIT_OPER_15&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x8000): Reserved
    <br/>
    Thorlabs defined MEASRUEMENT STATUS REGISTER bits
    <br/>
    TLPM_STATBIT_MEAS_0&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0001): Reserved
    <br/>
    TLPM_STATBIT_MEAS_1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0002): Reserved
    <br/>
    TLPM_STATBIT_MEAS_2&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0004): Reserved
    <br/>
    TLPM_STATBIT_MEAS_3&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0008): Reserved
    <br/>
    TLPM_STATBIT_MEAS_4&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0010): Reserved
    <br/>
    TLPM_STATBIT_MEAS_5&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0020): Reserved
    <br/>
    TLPM_STATBIT_MEAS_6&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0040): Reserved
    <br/>
    TLPM_STATBIT_MEAS_7&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0080): Reserved
    <br/>
    TLPM_STATBIT_MEAS_8&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0100): Reserved
    <br/>
    TLPM_STATBIT_MEAS_9&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0200): Reserved
    <br/>
    TLPM_STATBIT_MEAS_10&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0400): Reserved
    <br/>
    TLPM_STATBIT_MEAS_11&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0800): Reserved
    <br/>
    TLPM_STATBIT_MEAS_12&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x1000): Reserved
    <br/>
    TLPM_STATBIT_MEAS_13&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x2000): Reserved
    <br/>
    TLPM_STATBIT_MEAS_14&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x4000): Reserved
    <br/>
    TLPM_STATBIT_MEAS_15&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x8000): Reserved
    <br/>
    Thorlabs defined Auxiliary STATUS REGISTER bits
    <br/>
    TLPM_STATBIT_AUX_NTC&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0001): Auxiliary NTC temperature sensor connected.
    <br/>
    TLPM_STATBIT_AUX_EMM&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0002): External measurement module connected.
    <br/>
    TLPM_STATBIT_AUX_2&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0004): Reserved
    <br/>
    TLPM_STATBIT_AUX_3&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0008): Reserved
    <br/>
    TLPM_STATBIT_AUX_EXPS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0010): External power supply connected
    <br/>
    TLPM_STATBIT_AUX_BATC&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0020): Battery charging
    <br/>
    TLPM_STATBIT_AUX_BATL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0040): Battery low
    <br/>
    TLPM_STATBIT_AUX_IPS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0080): Apple(tm) authentification supported.
    <br/>
    TLPM_STATBIT_AUX_IPF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0100): Apple(tm) authentification failed.
    <br/>
    TLPM_STATBIT_AUX_9&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0200): Reserved
    <br/>
    TLPM_STATBIT_AUX_10&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0400): Reserved
    <br/>
    TLPM_STATBIT_AUX_11&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x0800): Reserved
    <br/>
    TLPM_STATBIT_AUX_12&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x1000): Reserved
    <br/>
    TLPM_STATBIT_AUX_13&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x2000): Reserved
    <br/>
    TLPM_STATBIT_AUX_14&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x4000): Reserved
    <br/>
    TLPM_STATBIT_AUX_15&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(0x8000): Reserved
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassStatus%20Register.html">Status Register</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>