/***************************************************/
/* MINIMAL STYLES */
/* The following section defines styles that every HTML Help project should need. */

/* Specifies White Background color */
body {background-color:#ffffff}

/* The default style of P is red to alert you that you need to apply a style class, such as Body. */
P { margin-top:6.00pt; margin-bottom:6.00pt; font-family:Verdana, sans-serif; font-size:8pt; color: #000000; }

BR { font-size:4.00pt; }

H1,H2,H3,H4,H5,H6 { color:#000000 }

/* Use H1 for all topic headings. */
H1 { margin-top:3.00pt; margin-bottom:3.00pt; font-family:Verdana, sans-serif; font-size:12pt; font-weight:bold;  }

/* not in minimal.css; LabWindows/CVI-specific */
H1.function{ margin-top:3.00pt; margin-bottom:3.00pt; font-family:Verdana, sans-serif; font-size:12pt; font-weight:bold; color:#000000; }	

/* Use H2 for second-level headings. */
H2 { margin-top:9.00pt; margin-bottom:3.00pt; font-family:Verdana, sans-serif; font-size:10pt; font-weight:bold;  }

/* not in minimal.css; LabWindows/CVI-specific */ 
H2.purpose{ margin-top:9.00pt; margin-bottom:3.00pt; font-family:Verdana; font-size:10pt; font-weight:bold; color:#000000; }	

/* not in minimal.css; LabWindows/CVI-specific */  
H2.code{ font-family:"Courier New"; font-size:10pt; color:#000000;} 		   

/* Use H3 for third-level headings. */
H3 { margin-top:6.00pt; margin-bottom:3.00pt; font-family:Verdana, sans-serif; font-size:8pt; font-weight:bold;  }

/* Use H4 for fourth-level headings. */
H4 { margin-top:3.00pt; margin-bottom:3.00pt; font-family:Verdana, sans-serif; font-size:8pt; font-weight:bold;  }

/* H5 and H6 have the same definition as H4 because you should not need this level of heading in one topic. If you need to use H5 or H6, consider breaking up your topic into more than one topic. */
H5 { margin-top:3.00pt; margin-bottom:3.00pt; font-family:Verdana, sans-serif; font-size:8pt; font-weight:bold;  }
H6 { margin-top:3.00pt; margin-bottom:3.00pt; font-family:Verdana, sans-serif; font-size:8pt; font-weight:bold;  }

/* Use the Body style class for normal paragraphs. */
P.Body { margin-top:6.00pt; margin-bottom:6.00pt; font-family:Verdana, sans-serif; font-size:8pt; color:#000000;  }

/* Not in minimal.css; LabWindows/CVI-specific. */
P.Equation { margin-top:6.00pt; margin-bottom:6.00pt; font-family:Times; font-size:10pt; color:#000000; }

/* Not in minimal.css; LabWindows/CVI-specific. */
P.list{font-family:Verdana, sans-serif;
   	   font-size:8 pt; 
       line-height:1.4;
	   color:black;}

/* not in minimal.css; LabWindows/CVI-specific */	   
P.syntax{ text-align:left; text-indent:-65pt; margin-top:9pt; margin-bottom:0pt; margin-right:0pt; margin-left:65pt; font-size:8pt; font-weight:medium; font-style:Regular; font-family:"Courier New"; text-decoration:none; vertical-align:baseline; text-transform:none; color:#000000;}	
	   
	   
/* Use the Anchor style class for graphic references on a line by themselves. */
P.Anchor { margin-top:6.00pt; margin-bottom:6.00pt; font-family:Verdana, sans-serif; font-size:10pt; color:#000000;  }

/* Use the Indent style classes to indent a paragraph. If you need to indent text below a list item, use <br><br> to start the new paragraph within the same set of <li></li> tags. If you need to indent a list within another list, nest the indented list within the first list's set of <ol></ol> or <ul></ul> tags. */
/* Indent is not in minimal.css; LabWindows/CVI-specific. Used in only a few places.*/
P.Indent {text-indent:2em; margin-top:6.00pt; margin-bottom:3.00pt; font-size:8pt; color:#000000;}
P.Indent1 { margin-left:12.00pt; margin-top:6.00pt; margin-bottom:3.00pt; font-family:Verdana, sans-serif;font-size:8pt; color:#000000;  }
P.Indent2 { margin-left:24.00pt; margin-top:6.00pt; margin-bottom:3.00pt; font-family:Verdana, sans-serif; font-size:8pt; color:#000000;  }
P.Indent3 { margin-left:36.00pt; margin-top:6.00pt; margin-bottom:3.00pt; font-family:Verdana, sans-serif; font-size:8pt; color:#000000;  }

/*not in minimal.css; LabWindows/CVI-specific */
P.advanonly { margin-top:6.00pt; margin-bottom:6.00pt; font-family:Verdana, sans-serif; font-size:8pt; color:#000000;  }

/*not in minimal.css; LabWindows/CVI-specific. With P.ObsoleteContent, this style is used to mark obsolete functions. */
P.Obsolete {font-family:Verdana, sans-serif; font-size:10pt; color:#000000; border-width: 1.5pt; 
border-style: solid; border-color: #800000; border-bottom:0px; margin-bottom:0px; margin-top:15px; padding:3px; font-weight:bold; }

/*not in minimal.css; LabWindows/CVI-specific. With P.Obsolete, this style is used to mark obsolete functions. */
P.ObsoleteContent {font-family:Verdana, sans-serif; font-size:8pt; color:#800000; border-width: 1.5pt; border-style: solid; border-color: #800000; border-top:0px; margin-bottom:15px; margin-top:0px; padding:3px; }

/* Use the LI style for all list items. */
LI { margin-top:3.00pt; margin-bottom:3.00pt; font-family:Verdana, sans-serif; font-size:8pt; color:#000000; }


/* Use the OL style for numbered lists. You do not have to type the number for each list item in a numbered list. */
OL { margin-left:18.00pt; margin-top:3.00pt; margin-bottom:3.00pt; text-indent:0pt; font-family:Verdana, sans-serif; font-size:8pt; color:#000000; list-style-type: decimal; }

/* Use the OL style for numbered lists. Nested lists will use the bullet types according to the nesting scheme below */
ol ol {list-style-type:lower-alpha}
ol ol ol {list-style-type:decimal}
ol ol ol ol {list-style-type:lower-alpha}
ol ol ol ol ol {list-style-type:decimal}
ol ol ol ol ol ol {list-style-type:lower-alpha}
ol ol ol ol ol ol ol {list-style-type:decimal}
ol ol ol ol ol ol ol ol {list-style-type:lower-alpha}


/* Use the EquationNum style class for numbered lists of equations. You do not have to type the number for each list item in a numbered list. */
OL.EquationNum { margin-left:36.00pt; margin-top:3.00pt; margin-bottom:3.00pt; text-indent:0pt; font-family:Verdana, sans-serif; font-size:8pt; color:#000000; list-style-type: decimal; }

/* Use the List-abc style class for lettered lists. You do not have to type the letter for each list item in a lettered list. */
OL.List-abc { margin-left:18.00pt; margin-top:3.00pt; margin-bottom:3.00pt; text-indent:0pt; font-family:Verdana, sans-serif; font-size:8pt; color:#000000; list-style-type:lower-alpha; }

/* Use the UL style for bulleted lists. You do not have to type the bullet for each list item in a bulleted list. */
UL { margin-left:12.00pt; text-indent:0pt; margin-top:3.00pt; margin-bottom:3.00pt; font-family:Verdana, sans-serif; font-size:8pt; color:#000000; list-style-type: disc; }

/* Use the UL style for bulleted lists. Nested lists will use the bullet types according to the nesting scheme below. NOTE: hyphen.gif required in same directory as minimal.css */
ul ul {list-style-image:url(hyphen.gif)}
ul ul ul {list-style-image:none;list-style-type:disc}
ul ul ul ul {list-style-image:url(hyphen.gif)}
ul ul ul ul ul {list-style-image:none;list-style-type:disc}
ul ul ul ul ul ul {list-style-image:url(hyphen.gif)}
ul ul ul ul ul ul ul {list-style-image:none;list-style-type:disc}
ul ul ul ul ul ul ul ul ul{list-style-image:url(hyphen.gif)}

/* Use the List-Index style class for list without bullets. */
UL.List-Index { margin-left:12.00pt; margin-top:3.00pt; margin-bottom:3.00pt; text-indent:0pt; font-family:Verdana, sans-serif; font-size:8pt; color:#000000; list-style-type: none; }

/* Use the List-Box style class for bulleted lists with boxes instead of bullets. You do not have to type the box for each list item in a the list. */ 
UL.List-Box { margin-left:12.00pt; margin-top:3.00pt; margin-bottom:3.00pt; text-indent:0pt; font-family:Verdana, sans-serif; font-size:8pt; color:#000000; list-style-type: square; }

/* Use the List-Circle style class for bulleted lists with circles instead of bullets. You do not have to type the circle for each list item in a the list. */ 
UL.List-Circle { margin-left:12.00pt; margin-top:3.00pt; margin-bottom:3.00pt; text-indent:0pt; font-family:Verdana, sans-serif; font-size:8pt; color:#000000; list-style-type: circle; }

/* Use the List-Diamond style class for bulleted lists with diamonds instead of bullets. You do not have to type the diamond for each list item in a diamond list. */
UL.List-Diamond { margin-left:12.00pt; margin-top:3.00pt; margin-bottom:3.00pt; text-indent:0pt; font-family:Verdana, sans-serif; font-size:8pt; color:#000000; list-style-image:url(diamond.gif); }

/* Use the List-Hyphen style class for bulleted lists with hyphens instead of bullets. You do not have to type the hyphen for each list item in a hyphen list. */
UL.List-Hyphen { margin-left:12.00pt; margin-top:3.00pt; margin-bottom:3.00pt; text-indent:0pt; font-family:Verdana, sans-serif; font-size:8pt; color:#000000; list-style-image:url(hyphen.gif); }

/* Use the Borderless style class for tables that do not need borders, such as for 2-column or 3-column lists with no headings. */
Table.Borderless { border:none; }

/* Use the Bordered style class for tables that need borders. */
Table.Bordered { border-width: 1pt; border-style: solid; border-color: #000000; border-collapse: collapse; }

/* Use the Borderless-Wide style class for tables that do not need borders and that you want to stretch to fill the entire topic width. */
Table.Borderless-Wide { border:none; width:100%; }

/* Use the Bordered-Wide style class for tables that need borders and that you want to stretch to fill the entire topic width. */
Table.Bordered-Wide { border-width: 1pt; border-style: solid; border-color: #000000; width:100%; border-collapse: collapse; }

/* Use the TD style for table cells in Borderless or Borderless-Wide tables. Added margin values to the minimal style. */
TD { font-family:Verdana, sans-serif; font-size:8pt; color:#000000; vertical-align:top; padding:3px; margin-top:3.00pt; margin-bottom:3.00pt; }

/* not in minimal.css; LabWindows/CVI-specific */
TD.tablehead{ margin-top:3.00pt; margin-bottom:3.00pt; font-family:Verdana, sans-serif; font-size:8pt; font-weight:bold; color:#000000; vertical-align:top; padding:3px; text-align:left; } 

TD.paramName { margin-top:3.00pt; margin-bottom:3.00pt; font-family:Verdana, sans-serif; font-size:8pt; font-weight:bold; color:#000000; vertical-align:top; padding:3px; }      


/* Use the Bordered style class for table cells in Bordered or Bordered-Wide tables. Added the margin values to minimal style. */
TD.Bordered { font-family:Verdana, sans-serif; font-size:8pt; color:#000000; vertical-align:top; border-width: 1pt; border-style: solid; border-color: #000000; margin-top:3.00pt; margin-bottom:3.00pt; padding:3px; }

/* TD indent styles not in minimal.css; LabWindows/CVI-specific */   
TD.indent{ valign:"top"; text-indent:2em; margin-left:65pt; }

TD.indent2{ text-indent:3em; margin-left:75pt; }	

TD.indent3{ text-indent:4em; margin-left:85pt; }	

/* Use the Icon style class for table cells that contain note, caution, warning, or tip icons, or LabVIEW datatype terminals. */
TD.Icon { width:40px; vertical-align:top; }

/* Use the Table-cell-8pt style class for table cells in tables that contain a lot of information and need smaller text to fit it all on one screen to prevent a horizontal scroll bar from appearing. */
TD.Table-cell-8pt { font-family:Verdana, sans-serif; font-size:7pt; color:#000000;  }

/* Use the TH style for table heading cells in Borderless or Borderless-Wide tables. */
TH { font-family:Verdana, sans-serif; font-size:8pt; color:#000000; font-weight:bold; padding:3px; }

/* Use the Bordered style class for table heading cells in Bordered or Bordered-Wide tables. */
TH.Bordered { font-family:Verdana, sans-serif; font-size:8pt; color:#000000; font-weight:bold;  border-width: 1pt; border-style: solid; border-color: #000000; }

/* Use the Table-cell-8pt style class for table heading cells in tables that contain a lot of information and need smaller text to fit it all on one screen to prevent a horizontal scroll bar from appearing. */
TH.Table-Head-8pt { font-family:Verdana, sans-serif; font-size:7pt; color:#000000; font-weight:bold; }

/* Use the Left-Align style class for table heading cells that you want to left align instead of center align. */
TH.Left-Align { font-family:Verdana, sans-serif; font-size:8pt; color:#000000; font-weight:bold; padding:3px; text-align:left; }

/* This style applies only to Samples */		
A.Sample{font-family:"Courier New";
		 font-size:8pt;
		 cursor:hand; 
		 text-decoration:underline;
		 color:green;}	

/* this style is for use only in Samples and Toolslib; not in minimal.css */
P.BodyAfter{margin-top:0.00pt;
margin-bottom:6.00pt;
font-family:Verdana, sans-serif;
font-size:8pt;
color:black;}

/* this style is for use only in Samples and Toolslib; not in minimal.css */
P.BodyBefore{margin-top:12.00pt;
margin-bottom:0.00pt;
font-family:"Courier New";
font-size:8pt;
color:black;}			 

/***************************************************/
/* CHARACTER FORMATS */
/* The following section defines character formats that every HTML Help project should need. */

/* Use the Dark-Red format for warnings or cautions. */
#Dark-Red { color: #800000 }

/* Use the Monospace format for code or syntax examples. */
#Monospace { font-family: Courier New; font-size: 8pt; }

/* Use the Monospace-Bold format for messages and responses that the computer automatically prints to the screen. */
#Monospace-Bold { font-family: Courier New; font-size: 8pt; font-weight: bold; }

/* Use the Monospace-Italic format to denote text that is a placeholder for a word or value that the user must supply. */
#Monospace-Italic { font-family: Courier New; font-size: 8pt; font-style: italic; }

/* Use the Platform format to denote a specific platform. */
#Platform { color: #0000FF; font-weight: bold; }

/* Use the Symbol format for characters not in the Verdana character set. Use this format sparingly. When possible, you should use the correct ASCII code for the symbol or use a graphic to recreate the symbol. */
#Symbol { font-family: Symbol; font-size: 8pt; }

/* CHARACTER FORMATS Updated to conform with the CSE HTML Validator Pro */
/* The following section defines character formats that every HTML Help project should need. */

/* Use the Dark-Red format for warnings or cautions. */
.Dark-Red { color: #800000 }

/* Use the Monospace format for code or syntax examples. */
.Monospace { font-family: Courier New; font-size: 8pt; }

/* Use the Monospace-Bold format for messages and responses that the computer automatically prints to the screen. */
.Monospace-Bold { font-family: Courier New; font-size: 8pt; font-weight: bold; }

/* Use the Monospace-Italic format to denote text that is a placeholder for a word or value that the user must supply. */
.Monospace-Italic { font-family: Courier New; font-size: 8pt; font-style: italic; }

/* Use the Platform format to denote a specific platform. */
.Platform { color: #0000FF; font-weight: bold; }

/* Use the Symbol format for characters not in the Verdana character set. Use this format sparingly. When possible, you should use the correct ASCII code for the symbol or use a graphic to recreate the symbol. */
.Symbol { font-family: Symbol; font-size: 8pt; }

/* Use the Red-text format to call attention to text that needs  information added or edited by techcomm */
.Red-text {color: #FF0000;}

/* Use the Green-Underline format for the green defintion in the conventions topic */
.Green-Underline {color: #007700;	text-decoration : underline;}

/* Use the glossButton format for the glossary buttons used in the glossary topic */
.glossButton { font-family:Verdana, sans-serif; font-size:12px; color:black; }

/* Use for text sections and hyphenated words that should not break at line wraps */
span.nobreak{white-space:nowrap}

/* The following styles define the color of links. */
a:link { color: #007700; font-family:Verdana, sans-serif;}
a:hover { color: #FF0000; font-family:Verdana, sans-serif;}
a:active { color: #FF0000; font-family:Verdana, sans-serif;}
a:visited { color: #7F007F; font-family:Verdana, sans-serif;}

/***************************************************/
/* ACTIVITY STYLES */
/* The following section defines styles that only tutorial-style HTML Help projects should need. */

/* Use the Activity-Objective style class for the objective at the beginning of an activity topic. */
H3.Activity-Objective { color: #800000; }

/***************************************************/
/* CODE STYLES */
/* The following section defines styles that you need to format entire sections of code or syntax examples. If you have just a few words you need to format as a code or syntax example, use the Monospace character format. */

P.Code { margin-top:3.00pt; margin-bottom:0.00pt; font-family:Courier New; font-size:8pt; color:#000000;  }
P.Code1 { margin-left:12.00pt; margin-top:3.00pt; margin-bottom:0.00pt; font-family:Courier New; font-size:8pt; color:#000000;  }
P.Code2 { margin-left:24.00pt; margin-top:3.00pt; margin-bottom:0.00pt; font-family:Courier New; font-size:8pt; color:#000000;  }
P.Code3 { margin-left:36.00pt; margin-top:3.00pt; margin-bottom:0.00pt; font-family:Courier New; font-size:8pt; color:#000000;  }

/***************************************************/
/* FUNCTION STYLES */
/* The following section defines styles that you might need to format function reference help. */

P.F-VI-Code4 { margin-left:48.00pt; margin-top:3.00pt; margin-bottom:0.00pt; font-family:Courier New; font-size:8pt; color:#000000;  }
P.F-VI-Code5 { margin-left:60.00pt; margin-top:3.00pt; margin-bottom:0.00pt; font-family:Courier New; font-size:8pt; color:#000000;  }
P.F-VI-Equation { margin-top:9.00pt; margin-bottom:9.00pt; font-family:Verdana, sans-serif; font-size:8pt; color:#000000;  }

OL.F-VI-EquationNum { margin-top:3.00pt; margin-bottom:3.00pt; text-indent:0pt; font-family:Verdana, sans-serif; font-size:8pt; color:#000000; list-style-type: decimal; }