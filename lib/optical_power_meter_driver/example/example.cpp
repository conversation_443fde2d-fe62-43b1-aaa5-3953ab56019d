﻿/******************************************************************************
 * Copyright 2021 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "rsfsc_log/rsfsc_log.h"
#include "thorlabs_optical_power_meter.h"
#include <cmath>
#include <thread>

using namespace robosense::lidar;

int main()
{
  robosense::lidar::ThorlabsOpticalPowerMeter meter(
    robosense::lidar::ThorlabsOpticalPowerMeter::PowerMeterType::PM101X);

  // 查找设备资源
  robosense::lidar::ThorlabsOpticalPowerMeter::findRsrc(
    robosense::lidar::ThorlabsOpticalPowerMeter::PowerMeterType::PM101X);

  if (meter.open("M01059212"))
  {
    RSFSCLog::getInstance()->info("open OK");
  }
  else
  {
    RSFSCLog::getInstance()->error("open NG : {}", meter.getErrorMsg());
  }

  double range { 0.02 };
  if (meter.setPowerRange(range))
  {
    RSFSCLog::getInstance()->info("setPowerRange OK : range {}W", range);
  }
  else
  {
    RSFSCLog::getInstance()->error("setPowerRange NG : {}", meter.getErrorMsg());
  }

  double power { NAN };
  std::this_thread::sleep_for(std::chrono::milliseconds(1000));
  for (int i = 0; i < 20; ++i)
  {
    if (meter.measurePower(power))
    {
      RSFSCLog::getInstance()->info("measurePower OK : {}mW", power * 1000);
    }
    else
    {
      RSFSCLog::getInstance()->error("measurePower NG : {}", meter.getErrorMsg());
    }
  }

  return 0;
}