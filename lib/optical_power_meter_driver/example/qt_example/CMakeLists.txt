﻿cmake_minimum_required(VERSION 3.10)
project(qt_power_meter_example)

# Find Qt5
find_package(Qt5 REQUIRED COMPONENTS Core Widgets)

# Enable Qt MOC, UIC, and RCC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Add definitions
add_definitions(-DQT_NO_KEYWORDS)

# Source files
set(SOURCES qt_example.cpp main_window.cpp)

set(HEADERS main_window.h)

# Create executable
add_executable(qt_power_meter_example ${SOURCES} ${HEADERS})

# Set include directories
target_include_directories(qt_power_meter_example PRIVATE ${Qt5Widgets_INCLUDE_DIRS})
target_include_directories(qt_power_meter_example PRIVATE ${PROJECT_SOURCE_DIR}/include)

# Link libraries
target_link_libraries(qt_power_meter_example PRIVATE Qt5::Core Qt5::Widgets optical_power_meter_driver
                                                     RSFSCLog::RSFSCLog)

# Set C++ standard
set_target_properties(
  qt_power_meter_example
  PROPERTIES CXX_STANDARD 17
             CXX_STANDARD_REQUIRED YES
             CXX_EXTENSIONS NO)
