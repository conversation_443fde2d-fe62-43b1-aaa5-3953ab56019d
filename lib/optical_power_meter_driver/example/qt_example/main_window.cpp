﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "main_window.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QString>
#include <QtCore/QCoreApplication>
#include <QtCore/QDateTime>
#include <QtCore/QTimer>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDoubleSpinBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMessageBox>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QTextBrowser>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>

namespace robosense::lidar
{

MainWindow::MainWindow(QWidget* _parent) :
  QMainWindow(_parent),
  power_meter_type_combo_(nullptr),
  serial_number_edit_(nullptr),
  find_devices_button_(nullptr),
  pushbutton_open_(nullptr),
  pushbutton_close_(nullptr),
  connection_status_label_(nullptr),
  power_range_spinbox_(nullptr),
  set_power_range_button_(nullptr),
  avg_count_spinbox_(nullptr),
  set_avg_count_button_(nullptr),
  wave_length_spinbox_(nullptr),
  set_wave_length_button_(nullptr),
  measure_power_button_(nullptr),
  start_continuous_button_(nullptr),
  stop_continuous_button_(nullptr),
  current_power_label_(nullptr),
  measurement_timer_(nullptr),
  driver_ptr_(std::make_unique<ThorlabsOpticalPowerMeter>(ThorlabsOpticalPowerMeter::PowerMeterType::PM100X)),
  is_connected_(false),
  is_measuring_(false)
{
  setupLayout();
  init();
}

MainWindow::~MainWindow() { driver_ptr_->close(); }

void MainWindow::setupLayout()
{
  setWindowTitle("Thorlabs Optical Power Meter Control Panel");
  resize(900, 700);

  auto* main_widget = new QWidget(this);
  setCentralWidget(main_widget);
  auto* main_layout = new QVBoxLayout(main_widget);

  // Device Connection Group
  auto* connection_group  = new QGroupBox("设备连接");
  auto* connection_layout = new QGridLayout(connection_group);

  connection_layout->addWidget(new QLabel("光功率计类型:"), 0, 0);
  power_meter_type_combo_ = new QComboBox();
  power_meter_type_combo_->addItem("PM100X", static_cast<int>(ThorlabsOpticalPowerMeter::PowerMeterType::PM100X));
  power_meter_type_combo_->addItem("PM101X", static_cast<int>(ThorlabsOpticalPowerMeter::PowerMeterType::PM101X));
  power_meter_type_combo_->setCurrentIndex(1);  // Default to PM101X
  connection_layout->addWidget(power_meter_type_combo_, 0, 1);

  find_devices_button_ = new QPushButton("查找设备");
  connection_layout->addWidget(find_devices_button_, 0, 2);

  connection_layout->addWidget(new QLabel("光功率计编号:"), 1, 0);
  serial_number_edit_ = new QLineEdit();
  serial_number_edit_->setPlaceholderText("Enter device serial number (e.g., *********)");
  connection_layout->addWidget(serial_number_edit_, 1, 1);

  pushbutton_open_ = new QPushButton("连接");
  connection_layout->addWidget(pushbutton_open_, 1, 2);

  connection_status_label_ = new QLabel("状态: 断开");
  connection_status_label_->setStyleSheet("color: red; font-weight: bold;");
  connection_layout->addWidget(connection_status_label_, 2, 0);

  pushbutton_close_ = new QPushButton("断开");
  pushbutton_close_->setEnabled(false);
  connection_layout->addWidget(pushbutton_close_, 2, 1);

  main_layout->addWidget(connection_group);

  // Measurement Settings Group
  auto* settings_group  = new QGroupBox("功率计设置");
  auto* settings_layout = new QGridLayout(settings_group);

  settings_layout->addWidget(new QLabel("测量功率最大范围 (W):"), 0, 0);
  power_range_spinbox_ = new QDoubleSpinBox();
  power_range_spinbox_->setDecimals(4);
  power_range_spinbox_->setRange(0.0001, 1.0);
  power_range_spinbox_->setValue(0.02);
  settings_layout->addWidget(power_range_spinbox_, 0, 1);

  set_power_range_button_ = new QPushButton("设置测量功率最大范围");
  set_power_range_button_->setEnabled(false);
  settings_layout->addWidget(set_power_range_button_, 0, 2);

  settings_layout->addWidget(new QLabel("功率测试采样次数:"), 1, 0);
  avg_count_spinbox_ = new QSpinBox();
  avg_count_spinbox_->setRange(1, 10000);
  avg_count_spinbox_->setValue(500);
  settings_layout->addWidget(avg_count_spinbox_, 1, 1);

  set_avg_count_button_ = new QPushButton("设置功率测试采样次数");
  set_avg_count_button_->setEnabled(false);
  settings_layout->addWidget(set_avg_count_button_, 1, 2);

  settings_layout->addWidget(new QLabel("波长 (nm):"), 2, 0);
  wave_length_spinbox_ = new QDoubleSpinBox();
  wave_length_spinbox_->setRange(400, 1100);
  wave_length_spinbox_->setValue(905.0);
  settings_layout->addWidget(wave_length_spinbox_, 2, 1);

  set_wave_length_button_ = new QPushButton("设置波长");
  set_wave_length_button_->setEnabled(false);
  settings_layout->addWidget(set_wave_length_button_, 2, 2);

  main_layout->addWidget(settings_group);

  // Measurement Controls Group
  auto* measurement_group  = new QGroupBox("功率测量");
  auto* measurement_layout = new QGridLayout(measurement_group);

  current_power_label_ = new QLabel("当前功率: -- mW");
  current_power_label_->setStyleSheet("font-size: 14px; font-weight: bold; color: blue;");
  measurement_layout->addWidget(current_power_label_, 0, 0, 1, 3);

  measure_power_button_ = new QPushButton("功率测量 (单次)");
  measure_power_button_->setEnabled(false);
  measurement_layout->addWidget(measure_power_button_, 1, 0);

  start_continuous_button_ = new QPushButton("开始 连续的");
  start_continuous_button_->setEnabled(false);
  measurement_layout->addWidget(start_continuous_button_, 1, 1);

  stop_continuous_button_ = new QPushButton("停止 连续的");
  stop_continuous_button_->setEnabled(false);
  measurement_layout->addWidget(stop_continuous_button_, 1, 2);

  main_layout->addWidget(measurement_group);

  // Create timer for continuous measurement
  measurement_timer_ = new QTimer(this);
  measurement_timer_->setInterval(100);  // 100ms interval

  // Connect all signals
  connectSignals();
}

void MainWindow::connectSignals()
{
  QObject::connect(find_devices_button_, &QPushButton::clicked, this, &MainWindow::slotFindDevices);
  QObject::connect(pushbutton_open_, &QPushButton::clicked, this, &MainWindow::slotOpen);
  QObject::connect(pushbutton_close_, &QPushButton::clicked, this, &MainWindow::slotClose);
  QObject::connect(set_power_range_button_, &QPushButton::clicked, this, &MainWindow::slotSetPowerRange);
  QObject::connect(set_avg_count_button_, &QPushButton::clicked, this, &MainWindow::slotSetAvgCount);
  QObject::connect(set_wave_length_button_, &QPushButton::clicked, this, &MainWindow::slotSetWaveLength);
  QObject::connect(measure_power_button_, &QPushButton::clicked, this, &MainWindow::slotMeasurePower);
  QObject::connect(start_continuous_button_, &QPushButton::clicked, this, &MainWindow::slotStartContinuousMeasurement);
  QObject::connect(stop_continuous_button_, &QPushButton::clicked, this, &MainWindow::slotStopContinuousMeasurement);
  QObject::connect(measurement_timer_, &QTimer::timeout, this, &MainWindow::onMeasurementTimer);
}

void MainWindow::init() {}

void MainWindow::updateConnectionStatus(bool _connected)
{
  is_connected_ = _connected;

  if (_connected)
  {
    connection_status_label_->setText("状态: 已连接");
    connection_status_label_->setStyleSheet("color: green; font-weight: bold;");

    pushbutton_open_->setEnabled(false);
    pushbutton_close_->setEnabled(true);
    set_power_range_button_->setEnabled(true);
    set_avg_count_button_->setEnabled(true);
    set_wave_length_button_->setEnabled(true);
    measure_power_button_->setEnabled(true);
    start_continuous_button_->setEnabled(true);
  }
  else
  {
    connection_status_label_->setText("状态: 断开");
    connection_status_label_->setStyleSheet("color: red; font-weight: bold;");

    pushbutton_open_->setEnabled(true);
    pushbutton_close_->setEnabled(false);
    set_power_range_button_->setEnabled(false);
    set_avg_count_button_->setEnabled(false);
    set_wave_length_button_->setEnabled(false);
    measure_power_button_->setEnabled(false);
    start_continuous_button_->setEnabled(false);
    stop_continuous_button_->setEnabled(false);

    current_power_label_->setText("当前功率: -- mW");
  }
}

void MainWindow::slotFindDevices()
{
  LOG_INFO("Searching for devices...");

  auto power_meter_type =
    static_cast<ThorlabsOpticalPowerMeter::PowerMeterType>(power_meter_type_combo_->currentData().toInt());

  bool found = ThorlabsOpticalPowerMeter::findRsrc(power_meter_type);

  if (found)
  {
    LOG_INFO("Device search completed. Check console output for available devices.");
  }
  else
  {
    LOG_ERROR("No devices found or search failed.");
  }
}

void MainWindow::slotOpen()
{
  QString serial_number = serial_number_edit_->text().trimmed();
  if (serial_number.isEmpty())
  {
    QMessageBox::warning(this, "Warning", "Please enter a serial number");
    return;
  }

  LOG_INFO("Connecting to device: {}", serial_number.toStdString());

  double power_range = power_range_spinbox_->value();
  double wave_length = wave_length_spinbox_->value();

  bool success = driver_ptr_->open(serial_number.toStdString(), power_range, wave_length);

  if (success)
  {
    updateConnectionStatus(true);
    LOG_INFO("Successfully connected to device: {}", serial_number.toStdString());

    // Set initial average count
    slotSetAvgCount();
  }
  else
  {
    LOG_ERROR("Failed to connect: {}", driver_ptr_->getErrorMsg());
  }
}

void MainWindow::slotClose()
{
  if (!is_connected_)
  {
    return;
  }

  // Stop continuous measurement if running
  if (is_measuring_)
  {
    slotStopContinuousMeasurement();
  }

  LOG_INFO("Disconnecting from device...");

  bool success = driver_ptr_->close();
  updateConnectionStatus(false);

  if (success)
  {
    LOG_INFO("Successfully disconnected from device");
  }
  else
  {
    LOG_ERROR("Disconnect warning: {}", driver_ptr_->getErrorMsg());
  }
}

void MainWindow::slotSetPowerRange()
{
  if (!is_connected_)
  {
    LOG_ERROR("Device not connected");
    return;
  }

  double power_range = power_range_spinbox_->value();
  LOG_INFO("Setting power range to: {} W", power_range);

  bool success = driver_ptr_->setPowerRange(power_range);

  if (success)
  {
    LOG_INFO("Power range set successfully: {} W", power_range);
  }
  else
  {
    LOG_ERROR("Failed to set power range: {}", driver_ptr_->getErrorMsg());
  }
}

void MainWindow::slotSetAvgCount()
{
  if (!is_connected_)
  {
    LOG_ERROR("Device not connected");
    return;
  }

  int avg_count = avg_count_spinbox_->value();
  LOG_INFO("Setting average count to: {}", avg_count);

  bool success = driver_ptr_->setAvgCnt(avg_count);

  if (success)
  {
    LOG_INFO("Average count set successfully: {}", avg_count);
  }
  else
  {
    LOG_ERROR("Failed to set average count: {}", driver_ptr_->getErrorMsg());
  }
}

void MainWindow::slotMeasurePower()
{
  if (!is_connected_)
  {
    LOG_ERROR("Device not connected");
    return;
  }

  double power = 0.0;
  bool success = driver_ptr_->measurePower(power);

  if (success)
  {
    double power_mw = power * 1000.0;
    current_power_label_->setText(QString("当前功率: %1 mW").arg(power_mw, 0, 'f', 3));
    LOG_INFO("Single measurement: {} mW", power_mw);
  }
  else
  {
    LOG_ERROR("Failed to measure power: {}", driver_ptr_->getErrorMsg());
  }
}

void MainWindow::slotStartContinuousMeasurement()
{
  if (!is_connected_)
  {
    LOG_ERROR("Device not connected");
    return;
  }

  is_measuring_ = true;
  measurement_timer_->start();

  start_continuous_button_->setEnabled(false);
  stop_continuous_button_->setEnabled(true);

  LOG_INFO("Started continuous measurement");
}

void MainWindow::slotStopContinuousMeasurement()
{
  if (!is_measuring_)
  {
    return;
  }

  is_measuring_ = false;
  measurement_timer_->stop();

  start_continuous_button_->setEnabled(true);
  stop_continuous_button_->setEnabled(false);

  LOG_INFO("Stopped continuous measurement");
}

void MainWindow::slotSetWaveLength()
{
  // Note: Wave length setting is typically done during open() call
  // This function is for demonstration purposes
  double wave_length = wave_length_spinbox_->value();
  LOG_INFO("Wave length setting: {} nm (Note: This is set during connection)", wave_length);
}

void MainWindow::onMeasurementTimer()
{
  if (!is_connected_ || !is_measuring_)
  {
    return;
  }

  double power = 0.0;
  bool success = driver_ptr_->measurePower(power);

  if (success)
  {
    double power_mw = power * 1000.0;
    current_power_label_->setText(QString("当前功率: %1 mW").arg(power_mw, 0, 'f', 3));
  }
  else
  {
    LOG_INFO("Continuous measurement failed: {}", driver_ptr_->getErrorMsg());
    slotStopContinuousMeasurement();
  }
}

}  // namespace robosense::lidar
