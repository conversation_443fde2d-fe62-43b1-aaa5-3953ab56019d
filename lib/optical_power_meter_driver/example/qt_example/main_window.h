﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef MAIN_WINDOW_H
#define MAIN_WINDOW_H

#include "thorlabs_optical_power_meter.h"
#include <QtWidgets/QMainWindow>
#include <memory>

class QPushButton;
class QComboBox;
class QLineEdit;
class QDoubleSpinBox;
class QSpinBox;
class QLabel;
class QTextEdit;
class QTimer;
class QGroupBox;
class QGridLayout;

namespace robosense::lidar
{

class MainWindow final : public QMainWindow
{
  Q_OBJECT
public:
  explicit MainWindow(QWidget* _parent = nullptr);
  MainWindow(MainWindow&&)      = delete;
  MainWindow(const MainWindow&) = delete;
  MainWindow& operator=(MainWindow&&) = delete;
  MainWindow& operator=(const MainWindow&) = delete;
  ~MainWindow() override;

protected Q_SLOTS:
  void slotFindDevices();
  void slotOpen();
  void slotClose();
  void slotSetPowerRange();
  void slotSetAvgCount();
  void slotMeasurePower();
  void slotStartContinuousMeasurement();
  void slotStopContinuousMeasurement();
  void slotSetWaveLength();
  void onMeasurementTimer();

private:
  void setupLayout();
  void init();
  void connectSignals();
  void updateConnectionStatus(bool _connected);

  // Device connection controls
  QComboBox* power_meter_type_combo_;
  QLineEdit* serial_number_edit_;
  QPushButton* find_devices_button_;
  QPushButton* pushbutton_open_;
  QPushButton* pushbutton_close_;
  QLabel* connection_status_label_;

  // Measurement settings controls
  QDoubleSpinBox* power_range_spinbox_;
  QPushButton* set_power_range_button_;
  QSpinBox* avg_count_spinbox_;
  QPushButton* set_avg_count_button_;
  QDoubleSpinBox* wave_length_spinbox_;
  QPushButton* set_wave_length_button_;

  // Measurement controls
  QPushButton* measure_power_button_;
  QPushButton* start_continuous_button_;
  QPushButton* stop_continuous_button_;
  QLabel* current_power_label_;

  // Timer for continuous measurement
  QTimer* measurement_timer_;

  // Power meter driver
  std::unique_ptr<ThorlabsOpticalPowerMeter> driver_ptr_;

  // Status flags
  bool is_connected_;
  bool is_measuring_;
};

}  // namespace robosense::lidar

#endif  // MAIN_WINDOW_H