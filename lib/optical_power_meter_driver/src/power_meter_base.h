﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef POWER_METER_BASE_H
#define POWER_METER_BASE_H

#include "TLPM.h"
#include <array>
#include <mutex>
#include <string>
#include <vector>

namespace robosense
{
namespace lidar
{
constexpr static int BUFFER_SIZE { 256 };

class PowerMeterBase
{
public:
  PowerMeterBase()                      = default;
  PowerMeterBase(PowerMeterBase&&)      = delete;
  PowerMeterBase(const PowerMeterBase&) = delete;
  PowerMeterBase& operator=(PowerMeterBase&&) = delete;
  PowerMeterBase& operator=(const PowerMeterBase&) = delete;
  virtual ~PowerMeterBase()                        = default;

  static std::vector<std::array<char, BUFFER_SIZE>>& getAllRes();

  virtual bool open(const std::string& _sn, const double _power_range, const double _wave_length) = 0;
  virtual bool close()                                                                            = 0;
  virtual bool measurePower(double& _power)                                                       = 0;
  virtual bool setAvgCnt(const int _cnt)                                                          = 0;
  virtual bool setPowerRange(const double _power_range)                                           = 0;
  [[nodiscard]] std::string getErrorMsg() const;
  void setError(const std::string& _err);
  void setLogIndex(const int _index);
  [[nodiscard]] int getLogIndex() const;
  static std::mutex& getMtx();

protected:
  std::string& getError();

private:
  std::string error_;
  static std::mutex mtx_;
  static std::vector<std::array<char, BUFFER_SIZE>> all_resource_name_;
  int log_index_ { -1 };
};

}  // namespace lidar
}  // namespace robosense

#endif  // POWER_METER_BASE_H
