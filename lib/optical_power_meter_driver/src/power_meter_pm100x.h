﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef POWER_METER_PM100X_H
#define POWER_METER_PM100X_H

#include "power_meter_base.h"

namespace robosense
{
namespace lidar
{

/**
 * @brief PM100X系列的功率计通讯，采用thorlabs封装的TL TLPM API进行通讯，具体参考头文件函数说明
 * 
 */

class PowerMeterPM100X : public PowerMeterBase
{
public:
  PowerMeterPM100X()                        = default;
  PowerMeterPM100X(PowerMeterPM100X&&)      = delete;
  PowerMeterPM100X(const PowerMeterPM100X&) = delete;
  PowerMeterPM100X& operator=(PowerMeterPM100X&&) = delete;
  PowerMeterPM100X& operator=(const PowerMeterPM100X&) = delete;
  ~PowerMeterPM100X() override;

  bool open(const std::string& _sn, const double _power_range, const double _wave_length) override;
  bool close() override;
  bool measurePower(double& _power) override;
  bool setAvgCnt(const int _cnt) override;
  bool setPowerRange(const double _power_range) override;

private:
  ViSession session_ { VI_NULL };
};
}  // namespace lidar
}  // namespace robosense

#endif  // POWER_METER_PM100X_H
