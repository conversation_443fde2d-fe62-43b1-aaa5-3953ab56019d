﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "thorlabs_optical_power_meter.h"
#include "power_meter_base.h"
#include "power_meter_pm100x.h"
#include <TLvisa.h>
#ifdef __linux__
#  include "power_meter_pm101x.h"
#endif
#include "rsfsc_log/rsfsc_log_macro.h"
#include <array>
#include <memory>
#include <string>

namespace robosense
{
namespace lidar
{

#define METER_PTR (static_cast<PowerMeterBase*>(power_meter_.get()))

namespace
{
std::shared_ptr<PowerMeterBase> createPowerMeter(const ThorlabsOpticalPowerMeter::PowerMeterType _type)
{
  std::shared_ptr<PowerMeterBase> obj { nullptr };
  std::string info { "current type is : " };

  switch (_type)
  {
  case ThorlabsOpticalPowerMeter::PowerMeterType::PM100X:
    obj = std::make_shared<PowerMeterPM100X>();
    info += "PM100X";
    break;
  case ThorlabsOpticalPowerMeter::PowerMeterType::PM101X:
#ifdef __linux__
    obj = std::make_shared<PowerMeterPM101X>();
#else
    obj = std::make_shared<PowerMeterPM100X>();
#endif
    info += "PM101X";
    break;
  default:
    LOG_ERROR("error, invalid data mode = {}", std::to_string(static_cast<int>(_type)));
    obj = std::shared_ptr<PowerMeterBase>();
    return obj;
  }

  LOG_INFO("{}", info);

  return obj;
}

bool findRsrc100x()
{
  ViUInt32 num_found = 0;
  std::array<ViChar, BUFFER_SIZE> model_name { "" };
  std::array<ViChar, BUFFER_SIZE> serial_number { "" };
  std::array<ViChar, BUFFER_SIZE> resource_name { "" };
  ViBoolean is_available = VI_FALSE;
  std::unique_lock<std::mutex> lck(PowerMeterBase::getMtx());  //查找多个OPM设备时，多线程确保线程安全
  std::vector<std::array<char, BUFFER_SIZE>> {}.swap(PowerMeterBase::getAllRes());
  if (VI_SUCCESS != TLPM_findRsrc(0, &num_found) || num_found < 1)
  {
    LOG_ERROR("OPM not found");
    return false;
  }
  LOG_INFO("找到可能设备数量:{}", num_found);
  for (unsigned int i = 0; i < num_found; ++i)
  {
    auto res = TLPM_getRsrcInfo(0, i, model_name.data(), serial_number.data(), VI_NULL, &is_available);
    if (VI_SUCCESS != res)
    {
      LOG_ERROR("TLPM_getRsrcInfo error, res = {}", res);
      continue;
    }
    if (VI_SUCCESS != TLPM_getRsrcName(0, i, resource_name.data()))
    {
      LOG_ERROR("TLPM_getRsrcName error");
      continue;
    }

    LOG_INFO("resource_name : {}", resource_name.data());
    PowerMeterBase::getAllRes().emplace_back(resource_name);
  }

  return true;
}

bool findRsrc101xLinux()
{
  ViUInt32 num_found = 0;
  std::array<ViChar, BUFFER_SIZE> resource_name { "" };
  ViSession default_rm      = 0;
  ViFindList rsrc_list      = 0;
  ViConstString rsrc_format = "USB0::0x1313?*INSTR";
  std::unique_lock<std::mutex> lck(PowerMeterBase::getMtx());  //查找多个OPM设备时，多线程确保线程安全
  std::vector<std::array<char, BUFFER_SIZE>> {}.swap(PowerMeterBase::getAllRes());
  // Open the default resource manager
  if (TL_viOpenDefaultRM(&default_rm) < VI_SUCCESS)
  {
    LOG_ERROR("OPM can not initialize DefaultRM");
    return false;
  }

  if (VI_SUCCESS != TL_viFindRsrc(default_rm, (ViString)rsrc_format, &rsrc_list, &num_found, resource_name.data()) ||
      num_found < 1)
  {
    LOG_ERROR("OPM not found, num_found = {}", num_found);
    return false;
  }
  LOG_INFO("找到可能设备数量:{}", num_found);
  for (unsigned int i = 0; i < num_found; ++i)
  {
    std::array<ViChar, BUFFER_SIZE> { "" }.swap(resource_name);
    if (VI_SUCCESS != TL_viFindNext(rsrc_list, resource_name.data()))
    {
      LOG_ERROR("TL_viFindNext() error");
      continue;
    }

    LOG_INFO("resource_name : {}", resource_name.data());
    PowerMeterBase::getAllRes().emplace_back(resource_name);
  }

  return true;
}
}  // namespace

ThorlabsOpticalPowerMeter::ThorlabsOpticalPowerMeter(const PowerMeterType _type)
{
  power_meter_ = createPowerMeter(_type);
  LOG_INDEX_INFO("pwr = {}", static_cast<void*>(power_meter_.get()));
}
ThorlabsOpticalPowerMeter::~ThorlabsOpticalPowerMeter() { close(); }
bool ThorlabsOpticalPowerMeter::findRsrc(const PowerMeterType _type)
{
  switch (_type)
  {
  case PowerMeterType::PM100X: return findRsrc100x();
  case PowerMeterType::PM101X:
#ifdef __linux__
    return findRsrc101xLinux();
#else
    return findRsrc100x();
#endif
  default: LOG_ERROR("error, invalid data mode = {}", std::to_string(static_cast<int>(_type))); return false;
  }
}
bool ThorlabsOpticalPowerMeter::open(const std::string& _sn, const double _power_range, const double _wave_length)
{
  return METER_PTR->open(_sn, _power_range, _wave_length);
}
bool ThorlabsOpticalPowerMeter::close() { return METER_PTR->close(); }
bool ThorlabsOpticalPowerMeter::measurePower(double& _power) { return METER_PTR->measurePower(_power); }
bool ThorlabsOpticalPowerMeter::setAvgCnt(int _cnt) { return METER_PTR->setAvgCnt(_cnt); }
bool ThorlabsOpticalPowerMeter::setPowerRange(const double _power_range)
{
  return METER_PTR->setPowerRange(_power_range);
}
std::string ThorlabsOpticalPowerMeter::getErrorMsg() const { return METER_PTR->getErrorMsg(); }
void ThorlabsOpticalPowerMeter::setLogIndex(const int _index) { METER_PTR->setLogIndex(_index); }
[[nodiscard]] int ThorlabsOpticalPowerMeter::getLogIndex() const { return METER_PTR->getLogIndex(); }

}  // namespace lidar
}  // namespace robosense