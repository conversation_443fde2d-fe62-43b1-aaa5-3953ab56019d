﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
// Define missing types and constants if necessary
#include <TLvisa.h>
#include <vector>
#define VI_ATTR_TERMCHAR_EN (0x3FFF0038)
#define VI_ATTR_TERMCHAR    (0x3FFF0037)
#define VI_TRUE             (1)
#define VI_NULL             (0)

#include "power_meter_pm101x.h"

#include "rsfsc_log/rsfsc_log_macro.h"
#include <array>
#include <sstream>
#include <string>

namespace robosense
{
namespace lidar
{

PowerMeterPM101X::~PowerMeterPM101X() { close(); }

bool PowerMeterPM101X::open(const std::string& _sn, const double _power_range, const double _wave_length)
{
  {
    std::unique_lock<std::mutex> lck(getMtx());  //查找多个OPM设备时，多线程确保线程安全
    ViSession default_rm = 0;
    getError()           = std::string();
    if (session_ != VI_NULL)
    {
      getError() = std::string("This object already connect an OPM");
      return false;
    }
    // char instrument_descriptor[TLPM_BUFFER_SIZE];
    std::array<ViChar, BUFFER_SIZE> resource_name { "" };
    ViBoolean is_found_sn = VI_FALSE;

    for (auto& iter : getAllRes())
    {
      LOG_INDEX_INFO("resource name = {}, sn = :{}", iter.data(), _sn);
      if (!_sn.empty() && (std::string(iter.data()).find(_sn) != std::string::npos))
      {
        resource_name = iter;
        is_found_sn   = VI_TRUE;
        LOG_INDEX_INFO("found, match sn : {}", _sn);
        break;  //found, match _sn
      }
    }

    if ((VI_FALSE == is_found_sn))
    {
      getError() = std::string("OPM not found or is not available");
      return false;
    }

    if (VI_SUCCESS != TL_viOpen(default_rm, resource_name.data(), VI_NULL, VI_NULL, &session_))
    {
      getError() = std::string("OPM open dev error");
      return false;
    }
  }

  // set the answer timeout
  if (VI_SUCCESS != TL_viSetAttribute(session_, VI_ATTR_TMO_VALUE, 5000))
  {
    getError() = std::string("OPM can not be init, set attribute error");
    return false;
  }

  // initialize setting
  if (!setWaveLength(static_cast<int>(_wave_length)) || !setPowerRange(_power_range))
  {
    return false;
  }

  return setAvgCnt(100);
}

bool PowerMeterPM101X::close()
{
  if (session_ == VI_NULL)
  {
    getError() = std::string("No OPM to close");
    return true;
  }
  if (VI_SUCCESS != TL_viClose(session_))
  {
    getError() = std::string("Failed to close OPM");
    return false;
  }
  session_ = VI_NULL;
  return true;
}

bool PowerMeterPM101X::measurePower(double& _power)
{
  if (session_ == VI_NULL)
  {
    getError() = std::string("OPM not open");
    return false;
  }

  try
  {
    if (VI_SUCCESS != TL_viWrite(session_, (ViBuf) "MEASURE:POWER?\n", 15, VI_NULL))
    {
      getError() = std::string("Measure Failed, write MEASURE:POWER");
      return false;
    }

    std::array<ViByte, BUFFER_SIZE> read_buf {};

    if (VI_SUCCESS != TL_viRead(session_, read_buf.data(), read_buf.size(), VI_NULL))
    {
      getError() = std::string("Measure Failed, read power");
      return false;
    }

    std::string str(reinterpret_cast<char*>(read_buf.data()));
    std::stringstream s_str(str);
    s_str >> _power;
  }
  catch (const std::exception&)
  {
    getError() = std::string("OPM was plug out");
    return false;
  }

  return true;
}

bool PowerMeterPM101X::setAvgCnt(int _cnt)
{
  if (session_ == VI_NULL)
  {
    getError() = std::string("OPM not open");
    return false;
  }

  std::string buf = "SENSE:AVER " + std::to_string(_cnt) + "\n";
  try
  {
    if (VI_SUCCESS != TL_viWrite(session_, (ViBuf)buf.data(), buf.size(), VI_NULL))
    {
      getError() = std::string("Measure Failed, write MEASURE:POWER");
      return false;
    }

    // TODO: 是否需要加上回读？
  }
  catch (const std::exception&)
  {
    getError() = std::string("OPM was plug out");
    return false;
  }
  return true;
}

bool PowerMeterPM101X::setPowerRange(const double _power_range)
{
  if (session_ == VI_NULL)
  {
    getError() = std::string("OPM not open");
    return false;
  }

  std::string buf = "SENSE:CURR:RANG " + std::to_string(_power_range) + "\n";
  try
  {
    if (VI_SUCCESS != TL_viWrite(session_, (ViBuf)buf.data(), buf.size(), VI_NULL))
    {
      getError() = std::string("setPowerRange Failed, write SENSE:CURR:RANG");
      return false;
    }

    // TODO: 是否需要加上回读？
  }
  catch (const std::exception&)
  {
    getError() = std::string("OPM was plug out");
    return false;
  }
  return true;
}

bool PowerMeterPM101X::setWaveLength(const int _wave_length)
{
  if (session_ == VI_NULL)
  {
    getError() = std::string("OPM not open");
    return false;
  }

  std::string write_buf = "SENS:CORR:WAV " + std::to_string(_wave_length) + "\n";

  try
  {
    if (VI_SUCCESS != TL_viWrite(session_, (ViBuf)write_buf.data(), write_buf.size(), VI_NULL))
    {
      getError() = std::string("setWaveLength Failed, write SENS:CORR:WAV");
      return false;
    }

    std::string write_buf { "SENS:CORR:WAV?" };
    if (VI_SUCCESS != TL_viWrite(session_, (ViBuf)write_buf.data(), write_buf.size(), VI_NULL))
    {
      getError() = std::string("setWaveLength Failed, write SENS:CORR:WAV?");
      return false;
    }

    std::array<ViByte, BUFFER_SIZE> read_buf {};
    if (VI_SUCCESS != TL_viRead(session_, read_buf.data(), read_buf.size(), VI_NULL))
    {
      getError() = std::string("setWaveLength Failed, read error");
      return false;
    }

    std::string str(reinterpret_cast<char*>(read_buf.data()));
    std::stringstream s_str(str);

    int read_value { 0 };
    s_str >> read_value;
    LOG_INDEX_INFO("setWaveLength, read_value is : {}nm", read_value);

    if (read_value != _wave_length)
    {
      getError() = "read average error, read value = ";
      getError() += std::to_string(read_value);
      getError() += ", set wave length = ";
      getError() += std::to_string(_wave_length);
      return false;
    }
  }
  catch (const std::exception&)
  {
    getError() = std::string("OPM was plug out");
    return false;
  }

  return true;
}

}  // namespace lidar
}  // namespace robosense